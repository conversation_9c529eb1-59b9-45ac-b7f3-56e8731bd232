import '../../domain/entities/product.dart';
import '../../domain/entities/brand.dart';
import '../../domain/entities/review.dart';

class SampleProductData {
  static final SampleProductData _instance = SampleProductData._internal();
  factory SampleProductData() => _instance;
  SampleProductData._internal();

  Future<List<Product>> getProducts() async {
    final now = DateTime.now();
    
    return [
      // Engine Parts
      Product(
        id: 'prod_engine_001',
        name: 'Premium Synthetic Engine Oil 5W-30',
        description: 'High-performance synthetic engine oil designed for modern engines. Provides superior protection against wear, deposits, and thermal breakdown.',
        shortDescription: 'Premium synthetic oil for superior engine protection',
        price: 114975, // TZS 114,975
        originalPrice: 149975, // TZS 149,975 (23% off)
        currency: 'TZS',
        imageUrl: 'https://via.placeholder.com/400x400/FF5722/FFFFFF?text=Engine+Oil',
        imageUrls: [
          'https://via.placeholder.com/400x400/FF5722/FFFFFF?text=Engine+Oil',
          'https://via.placeholder.com/400x400/FF5722/FFFFFF?text=Oil+Bottle',
          'https://via.placeholder.com/400x400/FF5722/FFFFFF?text=Oil+Label',
        ],
        categoryId: 'cat_engine',
        categoryName: 'Engine Parts',
        brandId: 'brand_mobil',
        brandName: 'Mobil 1',
        sku: 'MOB-5W30-4L',
        barcode: '1234567890123',
        stockQuantity: 45,
        minStockLevel: 10,
        stockStatus: StockStatus.inStock,
        condition: ProductCondition.new_,
        rating: 4.8,
        reviewCount: 127,
        isFeatured: true,
        isActive: true,
        isDigital: false,
        requiresShipping: true,
        tags: ['synthetic', 'premium', 'engine oil', '5W-30', 'mobil'],
        specifications: {
          'viscosity': '5W-30',
          'volume': '4L',
          'type': 'Fully Synthetic',
          'api_rating': 'SN/CF',
          'temperature_range': '-40°C to 150°C',
        },
        dimensions: const ProductDimensions(
          length: 25.0,
          width: 15.0,
          height: 30.0,
          weight: 4.2,
          unit: 'cm/kg',
        ),
        warranty: '1 year manufacturer warranty',
        manufacturer: 'ExxonMobil',
        countryOfOrigin: 'USA',
        compatibleVehicles: [
          'Toyota Corolla',
          'Honda Civic',
          'Nissan Sentra',
          'Mazda 3',
          'Subaru Impreza',
        ],
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      
      // Brake Parts
      Product(
        id: 'prod_brake_001',
        name: 'Ceramic Brake Pads Set - Front',
        description: 'High-quality ceramic brake pads designed for superior stopping power and reduced brake dust. Compatible with most passenger vehicles.',
        shortDescription: 'Premium ceramic brake pads for superior stopping',
        price: 224975, // TZS 224,975
        currency: 'TZS',
        imageUrl: 'https://via.placeholder.com/400x400/2196F3/FFFFFF?text=Brake+Pads',
        imageUrls: [
          'https://via.placeholder.com/400x400/2196F3/FFFFFF?text=Brake+Pads',
          'https://via.placeholder.com/400x400/2196F3/FFFFFF?text=Brake+Set',
        ],
        categoryId: 'cat_brakes',
        categoryName: 'Brake System',
        brandId: 'brand_brembo',
        brandName: 'Brembo',
        sku: 'BRE-CP-F001',
        stockQuantity: 23,
        minStockLevel: 5,
        stockStatus: StockStatus.inStock,
        condition: ProductCondition.new_,
        rating: 4.7,
        reviewCount: 89,
        isFeatured: true,
        isActive: true,
        isDigital: false,
        requiresShipping: true,
        tags: ['brake pads', 'ceramic', 'front', 'brembo', 'stopping power'],
        specifications: {
          'material': 'Ceramic',
          'position': 'Front',
          'pieces': '4 pads',
          'friction_coefficient': '0.35-0.45',
          'temperature_range': '0°C to 650°C',
        },
        warranty: '2 years or 50,000 km',
        manufacturer: 'Brembo S.p.A.',
        countryOfOrigin: 'Italy',
        compatibleVehicles: [
          'Toyota Camry',
          'Honda Accord',
          'Nissan Altima',
          'Mazda 6',
        ],
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
      
      // Electrical Parts
      Product(
        id: 'prod_electrical_001',
        name: 'LED Headlight Bulbs H7 - Ultra Bright',
        description: 'Ultra-bright LED headlight bulbs with 6000K color temperature. Easy plug-and-play installation with long lifespan.',
        shortDescription: 'Ultra-bright LED headlights with long lifespan',
        price: 87475, // TZS 87,475
        originalPrice: 124975, // TZS 124,975 (30% off)
        currency: 'TZS',
        imageUrl: 'https://via.placeholder.com/400x400/FFC107/FFFFFF?text=LED+Bulbs',
        imageUrls: [
          'https://via.placeholder.com/400x400/FFC107/FFFFFF?text=LED+Bulbs',
          'https://via.placeholder.com/400x400/FFC107/FFFFFF?text=LED+Light',
        ],
        categoryId: 'cat_electrical',
        categoryName: 'Electrical',
        brandId: 'brand_philips',
        brandName: 'Philips',
        sku: 'PHI-LED-H7',
        stockQuantity: 67,
        stockStatus: StockStatus.inStock,
        condition: ProductCondition.new_,
        rating: 4.6,
        reviewCount: 156,
        isFeatured: false,
        isActive: true,
        isDigital: false,
        requiresShipping: true,
        tags: ['LED', 'headlight', 'H7', 'bright', 'philips'],
        specifications: {
          'bulb_type': 'H7',
          'power': '25W',
          'lumens': '2500lm',
          'color_temperature': '6000K',
          'lifespan': '25,000 hours',
          'voltage': '12V',
        },
        warranty: '3 years manufacturer warranty',
        manufacturer: 'Philips Automotive',
        countryOfOrigin: 'Germany',
        compatibleVehicles: [
          'BMW 3 Series',
          'Mercedes C-Class',
          'Audi A4',
          'Volkswagen Golf',
        ],
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 3)),
      ),
      
      // Tires
      Product(
        id: 'prod_tire_001',
        name: 'All-Season Tire 205/55R16',
        description: 'Durable all-season tire with excellent grip and fuel efficiency. Designed for passenger cars with superior wet and dry performance.',
        shortDescription: 'Durable all-season tire with excellent grip',
        price: 324975, // TZS 324,975
        currency: 'TZS',
        imageUrl: 'https://via.placeholder.com/400x400/424242/FFFFFF?text=Tire',
        imageUrls: [
          'https://via.placeholder.com/400x400/424242/FFFFFF?text=Tire',
          'https://via.placeholder.com/400x400/424242/FFFFFF?text=Tire+Side',
        ],
        categoryId: 'cat_tires',
        categoryName: 'Tires',
        brandId: 'brand_michelin',
        brandName: 'Michelin',
        sku: 'MIC-AS-205-55-16',
        stockQuantity: 12,
        minStockLevel: 8,
        stockStatus: StockStatus.lowStock,
        condition: ProductCondition.new_,
        rating: 4.9,
        reviewCount: 203,
        isFeatured: true,
        isActive: true,
        isDigital: false,
        requiresShipping: true,
        tags: ['tire', 'all-season', '205/55R16', 'michelin', 'grip'],
        specifications: {
          'size': '205/55R16',
          'load_index': '91',
          'speed_rating': 'H',
          'tread_depth': '8.5mm',
          'sidewall': 'Standard',
          'season': 'All-Season',
        },
        dimensions: const ProductDimensions(
          length: 63.0,
          width: 20.5,
          height: 63.0,
          weight: 9.5,
          unit: 'cm/kg',
        ),
        warranty: '5 years or 80,000 km tread life warranty',
        manufacturer: 'Michelin',
        countryOfOrigin: 'France',
        compatibleVehicles: [
          'Honda Civic',
          'Toyota Corolla',
          'Nissan Sentra',
          'Hyundai Elantra',
        ],
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(hours: 12)),
      ),
      
      // Accessories
      Product(
        id: 'prod_accessory_001',
        name: 'Premium Car Air Freshener Set',
        description: 'Premium car air freshener with long-lasting fragrance. Set includes 3 different scents: Ocean Breeze, Vanilla, and Pine Forest.',
        shortDescription: 'Premium air freshener with long-lasting fragrance',
        price: 32475, // TZS 32,475
        currency: 'TZS',
        imageUrl: 'https://via.placeholder.com/400x400/009688/FFFFFF?text=Air+Freshener',
        imageUrls: [
          'https://via.placeholder.com/400x400/009688/FFFFFF?text=Air+Freshener',
        ],
        categoryId: 'cat_accessories',
        categoryName: 'Accessories',
        brandId: 'brand_febreze',
        brandName: 'Febreze',
        sku: 'FEB-AF-SET-3',
        stockQuantity: 156,
        stockStatus: StockStatus.inStock,
        condition: ProductCondition.new_,
        rating: 4.3,
        reviewCount: 78,
        isFeatured: false,
        isActive: true,
        isDigital: false,
        requiresShipping: true,
        tags: ['air freshener', 'fragrance', 'car accessories', 'febreze'],
        specifications: {
          'scents': 'Ocean Breeze, Vanilla, Pine Forest',
          'quantity': '3 pieces',
          'duration': '30 days each',
          'type': 'Hanging',
        },
        warranty: 'Satisfaction guarantee',
        manufacturer: 'Procter & Gamble',
        countryOfOrigin: 'USA',
        compatibleVehicles: ['Universal - All Vehicles'],
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
    ];
  }

  Future<List<Brand>> getBrands() async {
    final now = DateTime.now();
    
    return [
      Brand(
        id: 'brand_mobil',
        name: 'Mobil 1',
        description: 'Leading synthetic motor oil brand providing advanced engine protection',
        logoUrl: 'https://via.placeholder.com/100x100/FF5722/FFFFFF?text=M1',
        websiteUrl: 'https://www.mobil1.com',
        countryOfOrigin: 'USA',
        isActive: true,
        isFeatured: true,
        productCount: 25,
        averageRating: 4.7,
        createdAt: now.subtract(const Duration(days: 365)),
        updatedAt: now.subtract(const Duration(days: 30)),
      ),
      Brand(
        id: 'brand_brembo',
        name: 'Brembo',
        description: 'World leader in brake technology and high-performance braking systems',
        logoUrl: 'https://via.placeholder.com/100x100/2196F3/FFFFFF?text=BR',
        websiteUrl: 'https://www.brembo.com',
        countryOfOrigin: 'Italy',
        isActive: true,
        isFeatured: true,
        productCount: 18,
        averageRating: 4.8,
        createdAt: now.subtract(const Duration(days: 300)),
        updatedAt: now.subtract(const Duration(days: 15)),
      ),
      Brand(
        id: 'brand_philips',
        name: 'Philips',
        description: 'Innovative automotive lighting solutions and electrical components',
        logoUrl: 'https://via.placeholder.com/100x100/FFC107/FFFFFF?text=PH',
        websiteUrl: 'https://www.philips.com/automotive',
        countryOfOrigin: 'Netherlands',
        isActive: true,
        isFeatured: true,
        productCount: 32,
        averageRating: 4.6,
        createdAt: now.subtract(const Duration(days: 200)),
        updatedAt: now.subtract(const Duration(days: 10)),
      ),
      Brand(
        id: 'brand_michelin',
        name: 'Michelin',
        description: 'Premium tire manufacturer known for quality, performance, and innovation',
        logoUrl: 'https://via.placeholder.com/100x100/424242/FFFFFF?text=MI',
        websiteUrl: 'https://www.michelin.com',
        countryOfOrigin: 'France',
        isActive: true,
        isFeatured: true,
        productCount: 45,
        averageRating: 4.9,
        createdAt: now.subtract(const Duration(days: 400)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      Brand(
        id: 'brand_febreze',
        name: 'Febreze',
        description: 'Leading air care brand offering car fresheners and odor eliminators',
        logoUrl: 'https://via.placeholder.com/100x100/009688/FFFFFF?text=FB',
        websiteUrl: 'https://www.febreze.com',
        countryOfOrigin: 'USA',
        isActive: true,
        isFeatured: false,
        productCount: 12,
        averageRating: 4.2,
        createdAt: now.subtract(const Duration(days: 150)),
        updatedAt: now.subtract(const Duration(days: 20)),
      ),
    ];
  }

  Future<List<Review>> getReviews() async {
    final now = DateTime.now();
    
    return [
      Review(
        id: 'review_001',
        productId: 'prod_engine_001',
        userId: 'user_001',
        userName: 'John Mwangi',
        userAvatarUrl: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=JM',
        rating: 5.0,
        title: 'Excellent Engine Oil!',
        comment: 'This synthetic oil has made a huge difference in my car\'s performance. Engine runs smoother and quieter. Highly recommended!',
        imageUrls: [
          'https://via.placeholder.com/200x200/FF5722/FFFFFF?text=Oil+Review',
        ],
        isVerifiedPurchase: true,
        isHelpful: true,
        helpfulCount: 23,
        notHelpfulCount: 2,
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 10)),
      ),
      Review(
        id: 'review_002',
        productId: 'prod_brake_001',
        userId: 'user_002',
        userName: 'Sarah Kimani',
        userAvatarUrl: 'https://via.placeholder.com/50x50/E91E63/FFFFFF?text=SK',
        rating: 4.0,
        title: 'Good brake pads',
        comment: 'These brake pads work well and have good stopping power. Installation was straightforward. Only minor issue is they can be a bit noisy when cold.',
        imageUrls: [],
        isVerifiedPurchase: true,
        isHelpful: true,
        helpfulCount: 15,
        notHelpfulCount: 3,
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 15)),
      ),
      Review(
        id: 'review_003',
        productId: 'prod_electrical_001',
        userId: 'user_003',
        userName: 'David Ochieng',
        userAvatarUrl: 'https://via.placeholder.com/50x50/2196F3/FFFFFF?text=DO',
        rating: 5.0,
        title: 'Amazing brightness!',
        comment: 'These LED bulbs are incredibly bright and the installation was plug-and-play. Night driving is so much better now. Great value for money!',
        imageUrls: [
          'https://via.placeholder.com/200x200/FFC107/FFFFFF?text=LED+Night',
          'https://via.placeholder.com/200x200/FFC107/FFFFFF?text=LED+Bright',
        ],
        isVerifiedPurchase: true,
        isHelpful: true,
        helpfulCount: 31,
        notHelpfulCount: 1,
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
    ];
  }
}
