import '../domain/entities/product.dart';

/// Sample automotive products for testing and demonstration
/// This file provides a comprehensive list of automotive products
class SampleProducts {
  static final List<Product> products = [
    // 1. Engine Oil
    Product(
      id: 'prod_001',
      name: 'Castrol GTX High Mileage Motor Oil 5W-30',
      description:
          'Premium motor oil designed for vehicles with over 75,000 miles. Contains seal conditioners to help reduce leaks and minimize oil burn-off.',
      shortDescription: 'High mileage motor oil for older vehicles',
      price: 24.99,
      originalPrice: 29.99,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600',
      ],
      categoryId: 'cat_fluids',
      categoryName: 'Fluids & Lubricants',
      brandId: 'brand_castrol',
      brandName: 'Castrol',
      sku: 'CAST-GTX-5W30-5QT',
      barcode: '1234567890123',
      stockQuantity: 150,
      minStockLevel: 20,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.5,
      reviewCount: 324,
      isFeatured: true,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['motor oil', 'high mileage', 'synthetic blend', 'castrol'],
      specifications: {
        'viscosity': '5W-30',
        'volume': '5 quarts',
        'type': 'Synthetic Blend',
        'api_rating': 'SN',
      },
      dimensions: const ProductDimensions(
        length: 12.0,
        width: 8.0,
        height: 10.0,
        weight: 10.5,
        unit: 'inches/lbs',
      ),
      warranty: '1 year manufacturer warranty',
      manufacturer: 'Castrol',
      countryOfOrigin: 'USA',
      compatibleVehicles: [
        'Toyota Camry',
        'Honda Accord',
        'Ford F-150',
        'Chevrolet Silverado',
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),

    // 2. Brake Pads
    Product(
      id: 'prod_002',
      name: 'Bosch Blue Disc Brake Pad Set',
      description:
          'Premium ceramic brake pads offering superior stopping power, reduced noise, and extended pad life. Perfect for daily driving and light performance use.',
      shortDescription: 'Ceramic brake pads for superior stopping power',
      price: 89.99,
      originalPrice: null,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=500',
        'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=600',
      ],
      categoryId: 'cat_brakes',
      categoryName: 'Brakes & Suspension',
      brandId: 'brand_bosch',
      brandName: 'Bosch',
      sku: 'BOSCH-BLUE-BP1234',
      barcode: '2345678901234',
      stockQuantity: 75,
      minStockLevel: 10,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.7,
      reviewCount: 156,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['brake pads', 'ceramic', 'bosch', 'disc brakes'],
      specifications: {
        'material': 'Ceramic',
        'pad_count': '4 pads',
        'friction_rating': 'FF',
        'temperature_range': '-40°F to 750°F',
      },
      dimensions: const ProductDimensions(
        length: 6.0,
        width: 4.0,
        height: 1.0,
        weight: 3.2,
        unit: 'inches/lbs',
      ),
      warranty: '2 years or 24,000 miles',
      manufacturer: 'Bosch',
      countryOfOrigin: 'Germany',
      compatibleVehicles: ['BMW 3 Series', 'Audi A4', 'Mercedes C-Class'],
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),

    // 3. Air Filter
    Product(
      id: 'prod_003',
      name: 'K&N High-Flow Air Filter',
      description:
          'Washable and reusable air filter that provides excellent filtration and increased airflow for improved engine performance and fuel economy.',
      shortDescription: 'High-performance washable air filter',
      price: 49.99,
      originalPrice: 59.99,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?w=500',
      ],
      categoryId: 'cat_filters',
      categoryName: 'Filters',
      brandId: 'brand_kn',
      brandName: 'K&N',
      sku: 'KN-33-2304',
      stockQuantity: 200,
      minStockLevel: 25,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.6,
      reviewCount: 892,
      isFeatured: true,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['air filter', 'performance', 'washable', 'reusable'],
      specifications: {
        'filter_type': 'Cotton Gauze',
        'filtration_efficiency': '99%',
        'airflow_increase': '50%',
        'service_interval': '50,000 miles',
      },
      warranty: '10 years or 1 million miles',
      manufacturer: 'K&N Engineering',
      countryOfOrigin: 'USA',
      compatibleVehicles: ['Honda Civic', 'Toyota Corolla', 'Nissan Sentra'],
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),

    // 4. Spark Plugs
    Product(
      id: 'prod_004',
      name: 'NGK Iridium IX Spark Plugs (Set of 4)',
      description:
          'Premium iridium spark plugs offering superior ignitability, longer life, and improved fuel efficiency. Designed for modern engines.',
      shortDescription: 'Premium iridium spark plugs set',
      price: 32.99,
      originalPrice: null,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      ],
      categoryId: 'cat_ignition',
      categoryName: 'Ignition System',
      brandId: 'brand_ngk',
      brandName: 'NGK',
      sku: 'NGK-IRIDIUM-IX-4PK',
      stockQuantity: 120,
      minStockLevel: 15,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.8,
      reviewCount: 567,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['spark plugs', 'iridium', 'ngk', 'ignition'],
      specifications: {
        'electrode_material': 'Iridium',
        'gap': '0.044 inches',
        'thread_size': '14mm',
        'heat_range': '6',
      },
      warranty: '5 years or 100,000 miles',
      manufacturer: 'NGK',
      countryOfOrigin: 'Japan',
      compatibleVehicles: ['Mazda CX-5', 'Subaru Outback', 'Hyundai Elantra'],
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),

    // 5. Battery
    Product(
      id: 'prod_005',
      name: 'Optima RedTop Starting Battery',
      description:
          'High-performance AGM starting battery with exceptional starting power and vibration resistance. Perfect for trucks, SUVs, and performance vehicles.',
      shortDescription: 'High-performance AGM starting battery',
      price: 199.99,
      originalPrice: 229.99,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=500',
      ],
      categoryId: 'cat_electrical',
      categoryName: 'Electrical',
      brandId: 'brand_optima',
      brandName: 'Optima',
      sku: 'OPT-8002-002',
      stockQuantity: 45,
      minStockLevel: 5,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.4,
      reviewCount: 234,
      isFeatured: true,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['battery', 'agm', 'starting', 'optima'],
      specifications: {
        'voltage': '12V',
        'capacity': '720 CCA',
        'technology': 'AGM',
        'terminal_type': 'Top Post',
      },
      dimensions: const ProductDimensions(
        length: 10.0,
        width: 6.9,
        height: 7.5,
        weight: 38.8,
        unit: 'inches/lbs',
      ),
      warranty: '3 years free replacement',
      manufacturer: 'Optima Batteries',
      countryOfOrigin: 'USA',
      compatibleVehicles: ['Ford F-150', 'Chevrolet Silverado', 'Ram 1500'],
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),

    // 6. Tire Set
    Product(
      id: 'prod_006',
      name: 'Michelin Defender T+H All-Season Tires (Set of 4)',
      description:
          'Premium all-season tires offering exceptional longevity, fuel efficiency, and all-weather traction. MaxTouch Construction for even wear.',
      shortDescription: 'Premium all-season tire set',
      price: 599.99,
      originalPrice: 699.99,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600',
      ],
      categoryId: 'cat_tires',
      categoryName: 'Tires & Wheels',
      brandId: 'brand_michelin',
      brandName: 'Michelin',
      sku: 'MICH-DEF-TH-225-60R16',
      stockQuantity: 32,
      minStockLevel: 8,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.6,
      reviewCount: 1247,
      isFeatured: true,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['tires', 'all-season', 'michelin', 'fuel efficient'],
      specifications: {
        'size': '225/60R16',
        'load_index': '98',
        'speed_rating': 'H',
        'treadwear': '820',
        'utqg_traction': 'A',
        'utqg_temperature': 'A',
      },
      dimensions: const ProductDimensions(
        length: 26.6,
        width: 8.9,
        height: 26.6,
        weight: 24.0,
        unit: 'inches/lbs',
      ),
      warranty: '80,000 mile treadwear warranty',
      manufacturer: 'Michelin',
      countryOfOrigin: 'USA',
      compatibleVehicles: ['Honda Accord', 'Toyota Camry', 'Nissan Altima'],
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),

    // 7. LED Headlight Bulbs
    Product(
      id: 'prod_007',
      name: 'SEALIGHT H11 LED Headlight Bulbs (Pair)',
      description:
          'Ultra-bright LED headlight bulbs with 6000K cool white light, 300% brighter than halogen. Easy plug-and-play installation.',
      shortDescription: 'Ultra-bright LED headlight bulb pair',
      price: 39.99,
      originalPrice: 49.99,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      ],
      categoryId: 'cat_lighting',
      categoryName: 'Lighting',
      brandId: 'brand_sealight',
      brandName: 'SEALIGHT',
      sku: 'SEAL-H11-LED-6K',
      stockQuantity: 180,
      minStockLevel: 30,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.3,
      reviewCount: 678,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['led', 'headlights', 'h11', 'bright'],
      specifications: {
        'bulb_type': 'H11',
        'color_temperature': '6000K',
        'lumens': '12000LM',
        'power': '60W',
        'lifespan': '50,000 hours',
      },
      warranty: '2 years manufacturer warranty',
      manufacturer: 'SEALIGHT',
      countryOfOrigin: 'China',
      compatibleVehicles: ['Ford Focus', 'Hyundai Elantra', 'Kia Forte'],
      createdAt: DateTime.now().subtract(const Duration(days: 18)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),

    // 8. Floor Mats
    Product(
      id: 'prod_008',
      name: 'WeatherTech All-Weather Floor Mats (Front & Rear)',
      description:
          'Custom-fit all-weather floor mats providing maximum protection against dirt, mud, snow, and spills. Laser-measured for perfect fit.',
      shortDescription: 'Custom-fit all-weather floor mat set',
      price: 159.99,
      originalPrice: null,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      ],
      categoryId: 'cat_interior',
      categoryName: 'Interior Accessories',
      brandId: 'brand_weathertech',
      brandName: 'WeatherTech',
      sku: 'WT-FLMAT-CIVIC-2020',
      stockQuantity: 95,
      minStockLevel: 12,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.7,
      reviewCount: 445,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['floor mats', 'weathertech', 'all-weather', 'custom fit'],
      specifications: {
        'material': 'Thermoplastic Elastomer',
        'pieces': '4 pieces (front & rear)',
        'color': 'Black',
        'retention_system': 'Integrated nibs',
      },
      warranty: '3 years manufacturer warranty',
      manufacturer: 'WeatherTech',
      countryOfOrigin: 'USA',
      compatibleVehicles: ['Honda Civic 2016-2021'],
      createdAt: DateTime.now().subtract(const Duration(days: 35)),
      updatedAt: DateTime.now().subtract(const Duration(days: 4)),
    ),

    // 9. Car Wax
    Product(
      id: 'prod_009',
      name: 'Meguiar\'s Gold Class Car Wax',
      description:
          'Premium carnauba and synthetic polymer wax providing deep, rich shine and long-lasting protection. Easy application and removal.',
      shortDescription: 'Premium carnauba car wax',
      price: 16.99,
      originalPrice: 19.99,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      ],
      categoryId: 'cat_care',
      categoryName: 'Car Care',
      brandId: 'brand_meguiars',
      brandName: 'Meguiar\'s',
      sku: 'MEG-G7014J',
      stockQuantity: 250,
      minStockLevel: 40,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.5,
      reviewCount: 1156,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['car wax', 'carnauba', 'shine', 'protection'],
      specifications: {
        'type': 'Carnauba/Synthetic Blend',
        'volume': '14 oz',
        'application': 'Paste',
        'coverage': '8-10 vehicles',
      },
      warranty: '1 year manufacturer warranty',
      manufacturer: 'Meguiar\'s',
      countryOfOrigin: 'USA',
      compatibleVehicles: ['All vehicles'],
      createdAt: DateTime.now().subtract(const Duration(days: 12)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),

    // 10. Windshield Wipers
    Product(
      id: 'prod_010',
      name: 'Rain-X Latitude Windshield Wiper Blades (Pair)',
      description:
          'Premium beam-style wiper blades with Water Repellency technology. Contours to windshield for streak-free performance.',
      shortDescription: 'Premium beam-style wiper blade pair',
      price: 29.99,
      originalPrice: null,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      ],
      categoryId: 'cat_maintenance',
      categoryName: 'Maintenance',
      brandId: 'brand_rainx',
      brandName: 'Rain-X',
      sku: 'RX-LAT-24-18',
      stockQuantity: 140,
      minStockLevel: 20,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.2,
      reviewCount: 389,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['wiper blades', 'rain-x', 'beam style', 'water repellent'],
      specifications: {
        'style': 'Beam',
        'sizes': '24" & 18"',
        'technology': 'Water Repellency',
        'installation': 'Pre-installed adapters',
      },
      warranty: '1 year manufacturer warranty',
      manufacturer: 'Rain-X',
      countryOfOrigin: 'USA',
      compatibleVehicles: ['Toyota Prius', 'Honda CR-V', 'Mazda CX-5'],
      createdAt: DateTime.now().subtract(const Duration(days: 8)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),

    // 11. Transmission Fluid
    Product(
      id: 'prod_011',
      name: 'Valvoline MaxLife ATF Transmission Fluid',
      description:
          'Full synthetic automatic transmission fluid designed for high-mileage vehicles. Reduces leaks and conditions seals.',
      shortDescription: 'High-mileage ATF transmission fluid',
      price: 8.99,
      originalPrice: null,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      ],
      categoryId: 'cat_fluids',
      categoryName: 'Fluids & Lubricants',
      brandId: 'brand_valvoline',
      brandName: 'Valvoline',
      sku: 'VAL-ATF-MAXLIFE-1QT',
      stockQuantity: 200,
      minStockLevel: 30,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.4,
      reviewCount: 267,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['transmission fluid', 'atf', 'high mileage', 'synthetic'],
      specifications: {
        'type': 'Full Synthetic ATF',
        'volume': '1 quart',
        'viscosity': 'Multi-grade',
        'compatibility': 'Dexron VI, Mercon LV',
      },
      warranty: '1 year manufacturer warranty',
      manufacturer: 'Valvoline',
      countryOfOrigin: 'USA',
      compatibleVehicles: [
        'Ford Explorer',
        'Chevrolet Equinox',
        'Jeep Grand Cherokee',
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 22)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),

    // 12. Jumper Cables
    Product(
      id: 'prod_012',
      name: 'CARTMAN Heavy Duty Jumper Cables 12ft',
      description:
          '4-gauge heavy duty jumper cables with tangle-free design. Includes carrying case and safety gloves for emergency roadside assistance.',
      shortDescription: 'Heavy duty 12ft jumper cables with case',
      price: 34.99,
      originalPrice: 44.99,
      currency: 'USD',
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
      ],
      categoryId: 'cat_emergency',
      categoryName: 'Emergency & Safety',
      brandId: 'brand_cartman',
      brandName: 'CARTMAN',
      sku: 'CART-JC-4G-12FT',
      stockQuantity: 85,
      minStockLevel: 15,
      stockStatus: StockStatus.inStock,
      condition: ProductCondition.new_,
      rating: 4.1,
      reviewCount: 523,
      isFeatured: false,
      isActive: true,
      isDigital: false,
      requiresShipping: true,
      tags: ['jumper cables', 'emergency', 'heavy duty', 'roadside'],
      specifications: {
        'gauge': '4 AWG',
        'length': '12 feet',
        'amperage': '400 AMP',
        'clamps': 'Copper-plated',
        'includes': 'Carrying case, safety gloves',
      },
      warranty: '2 years manufacturer warranty',
      manufacturer: 'CARTMAN',
      countryOfOrigin: 'China',
      compatibleVehicles: ['All vehicles up to V8 engines'],
      createdAt: DateTime.now().subtract(const Duration(days: 40)),
      updatedAt: DateTime.now().subtract(const Duration(days: 6)),
    ),
  ];

  /// Get all products
  static List<Product> getAllProducts() => products;

  /// Get featured products
  static List<Product> getFeaturedProducts() =>
      products.where((product) => product.isFeatured).toList();

  /// Get products by category
  static List<Product> getProductsByCategory(String categoryId) =>
      products.where((product) => product.categoryId == categoryId).toList();

  /// Get products by brand
  static List<Product> getProductsByBrand(String brandId) =>
      products.where((product) => product.brandId == brandId).toList();

  /// Get in-stock products
  static List<Product> getInStockProducts() =>
      products.where((product) => product.isInStock).toList();

  /// Get products on sale
  static List<Product> getProductsOnSale() =>
      products.where((product) => product.isOnSale).toList();

  /// Search products by name or description
  static List<Product> searchProducts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return products
        .where(
          (product) =>
              product.name.toLowerCase().contains(lowercaseQuery) ||
              product.description.toLowerCase().contains(lowercaseQuery) ||
              product.tags.any(
                (tag) => tag.toLowerCase().contains(lowercaseQuery),
              ),
        )
        .toList();
  }
}
