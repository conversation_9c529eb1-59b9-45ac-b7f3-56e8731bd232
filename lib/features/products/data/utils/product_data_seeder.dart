import 'package:cloud_firestore/cloud_firestore.dart';

import '../sample_products.dart';
import '../models/product_model.dart';

/// Utility class to seed Firebase with sample product data
/// Call this from your app to populate the database
class ProductDataSeeder {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Upload all sample data to Firebase
  /// Returns true if successful, false if failed
  static Future<bool> seedDatabase() async {
    try {
      // Check if products already exist
      final existingProducts = await _firestore
          .collection('products')
          .limit(1)
          .get();
      
      if (existingProducts.docs.isNotEmpty) {
        print('Products already exist in database. Skipping seed.');
        return true;
      }

      // Upload products
      await _uploadProducts();
      
      // Upload categories
      await _uploadCategories();
      
      // Upload brands
      await _uploadBrands();
      
      print('✅ Database seeded successfully!');
      return true;
      
    } catch (e) {
      print('❌ Failed to seed database: $e');
      return false;
    }
  }

  /// Force re-seed (clears existing data first)
  static Future<bool> forceSeed() async {
    try {
      // Clear existing products
      await _clearCollection('products');
      await _clearCollection('categories');
      await _clearCollection('brands');
      
      // Upload fresh data
      await _uploadProducts();
      await _uploadCategories();
      await _uploadBrands();
      
      print('✅ Database force-seeded successfully!');
      return true;
      
    } catch (e) {
      print('❌ Failed to force-seed database: $e');
      return false;
    }
  }

  /// Upload sample products
  static Future<void> _uploadProducts() async {
    final sampleProducts = SampleProducts.getAllProducts();
    final batch = _firestore.batch();
    
    for (final product in sampleProducts) {
      final productModel = ProductModel.fromEntity(product);
      final docRef = _firestore.collection('products').doc(product.id);
      batch.set(docRef, productModel.toJson());
    }
    
    await batch.commit();
    print('📦 Uploaded ${sampleProducts.length} products');
  }

  /// Upload categories
  static Future<void> _uploadCategories() async {
    final categories = [
      {
        'id': 'cat_fluids',
        'name': 'Fluids & Lubricants',
        'description': 'Engine oils, transmission fluids, and other automotive lubricants',
        'isActive': true,
        'sortOrder': 1,
      },
      {
        'id': 'cat_brakes',
        'name': 'Brakes & Suspension',
        'description': 'Brake pads, rotors, suspension components',
        'isActive': true,
        'sortOrder': 2,
      },
      {
        'id': 'cat_filters',
        'name': 'Filters',
        'description': 'Air filters, oil filters, fuel filters',
        'isActive': true,
        'sortOrder': 3,
      },
      {
        'id': 'cat_ignition',
        'name': 'Ignition System',
        'description': 'Spark plugs, ignition coils, wires',
        'isActive': true,
        'sortOrder': 4,
      },
      {
        'id': 'cat_electrical',
        'name': 'Electrical',
        'description': 'Batteries, alternators, starters',
        'isActive': true,
        'sortOrder': 5,
      },
      {
        'id': 'cat_tires',
        'name': 'Tires & Wheels',
        'description': 'Tires, wheels, tire accessories',
        'isActive': true,
        'sortOrder': 6,
      },
      {
        'id': 'cat_lighting',
        'name': 'Lighting',
        'description': 'Headlights, taillights, LED bulbs',
        'isActive': true,
        'sortOrder': 7,
      },
      {
        'id': 'cat_interior',
        'name': 'Interior Accessories',
        'description': 'Floor mats, seat covers, organizers',
        'isActive': true,
        'sortOrder': 8,
      },
      {
        'id': 'cat_care',
        'name': 'Car Care',
        'description': 'Wax, polish, cleaning supplies',
        'isActive': true,
        'sortOrder': 9,
      },
      {
        'id': 'cat_maintenance',
        'name': 'Maintenance',
        'description': 'Wiper blades, belts, hoses',
        'isActive': true,
        'sortOrder': 10,
      },
      {
        'id': 'cat_emergency',
        'name': 'Emergency & Safety',
        'description': 'Jumper cables, emergency kits, safety equipment',
        'isActive': true,
        'sortOrder': 11,
      },
    ];

    final batch = _firestore.batch();
    
    for (final category in categories) {
      final docRef = _firestore.collection('categories').doc(category['id'] as String);
      batch.set(docRef, category);
    }
    
    await batch.commit();
    print('📂 Uploaded ${categories.length} categories');
  }

  /// Upload brands
  static Future<void> _uploadBrands() async {
    final brands = [
      {
        'id': 'brand_castrol',
        'name': 'Castrol',
        'description': 'Premium motor oils and lubricants',
        'isActive': true,
        'sortOrder': 1,
      },
      {
        'id': 'brand_bosch',
        'name': 'Bosch',
        'description': 'Automotive parts and accessories',
        'isActive': true,
        'sortOrder': 2,
      },
      {
        'id': 'brand_kn',
        'name': 'K&N',
        'description': 'High-performance air filters',
        'isActive': true,
        'sortOrder': 3,
      },
      {
        'id': 'brand_ngk',
        'name': 'NGK',
        'description': 'Spark plugs and ignition components',
        'isActive': true,
        'sortOrder': 4,
      },
      {
        'id': 'brand_optima',
        'name': 'Optima',
        'description': 'High-performance batteries',
        'isActive': true,
        'sortOrder': 5,
      },
      {
        'id': 'brand_michelin',
        'name': 'Michelin',
        'description': 'Premium tires and automotive services',
        'isActive': true,
        'sortOrder': 6,
      },
      {
        'id': 'brand_sealight',
        'name': 'SEALIGHT',
        'description': 'LED automotive lighting',
        'isActive': true,
        'sortOrder': 7,
      },
      {
        'id': 'brand_weathertech',
        'name': 'WeatherTech',
        'description': 'Automotive floor protection and accessories',
        'isActive': true,
        'sortOrder': 8,
      },
      {
        'id': 'brand_meguiars',
        'name': 'Meguiar\'s',
        'description': 'Car care and detailing products',
        'isActive': true,
        'sortOrder': 9,
      },
      {
        'id': 'brand_rainx',
        'name': 'Rain-X',
        'description': 'Windshield treatments and wiper blades',
        'isActive': true,
        'sortOrder': 10,
      },
      {
        'id': 'brand_valvoline',
        'name': 'Valvoline',
        'description': 'Motor oils and automotive fluids',
        'isActive': true,
        'sortOrder': 11,
      },
      {
        'id': 'brand_cartman',
        'name': 'CARTMAN',
        'description': 'Automotive tools and emergency equipment',
        'isActive': true,
        'sortOrder': 12,
      },
    ];

    final batch = _firestore.batch();
    
    for (final brand in brands) {
      final docRef = _firestore.collection('brands').doc(brand['id'] as String);
      batch.set(docRef, brand);
    }
    
    await batch.commit();
    print('🏢 Uploaded ${brands.length} brands');
  }

  /// Clear a collection
  static Future<void> _clearCollection(String collectionName) async {
    final snapshot = await _firestore.collection(collectionName).get();
    final batch = _firestore.batch();
    
    for (final doc in snapshot.docs) {
      batch.delete(doc.reference);
    }
    
    await batch.commit();
    print('🗑️ Cleared $collectionName collection');
  }
}
