# Sample Products Upload App

This Flutter app uploads sample automotive products to your Firebase Firestore database.

## What it uploads:

- **12 Sample Products**: Automotive parts and accessories
- **11 Categories**: Fluids, Brakes, Filters, Ignition, etc.
- **12 Brands**: Castrol, Bosch, K&N, NGK, etc.

## How to run:

1. Make sure Firebase is configured in your project
2. Run the Flutter app:

```bash
flutter run lib/scripts/upload_sample_products.dart
```

3. Click the "Upload Sample Products" button
4. Wait for the upload to complete

## Sample Products Included:

1. Castrol GTX Motor Oil - $24.99 (Featured, On Sale)
2. Bosch Brake Pads - $89.99
3. K&N Air Filter - $49.99 (Featured, On Sale)
4. NGK Spark Plugs - $32.99
5. Optima Battery - $199.99 (Featured, On Sale)
6. <PERSON>in Tires - $599.99 (Featured, On Sale)
7. LED Headlight Bulbs - $39.99 (On Sale)
8. WeatherTech Floor Mats - $159.99
9. <PERSON><PERSON><PERSON>'s Car Wax - $16.99 (On Sale)
10. Rain-X Wiper Blades - $29.99
11. Valvoline Transmission Fluid - $8.99
12. CARTMAN Jumper Cables - $34.99 (On Sale)

## Firebase Collections Created:

- `products` - Product data with specifications, pricing, images
- `categories` - Product categories for organization
- `brands` - Brand information and descriptions

## Note:

Run this script only once to populate your database. The script will create the collections and documents in your Firestore database.
