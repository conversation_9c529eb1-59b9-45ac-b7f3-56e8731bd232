import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../features/home/<USER>/models/banner_model.dart';
import '../firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  
  runApp(const BannerUploadApp());
}

class BannerUploadApp extends StatelessWidget {
  const BannerUploadApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Banner Upload Tool',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const BannerUploadScreen(),
    );
  }
}

class BannerUploadScreen extends StatefulWidget {
  const BannerUploadScreen({super.key});

  @override
  State<BannerUploadScreen> createState() => _BannerUploadScreenState();
}

class _BannerUploadScreenState extends State<BannerUploadScreen> {
  bool _isUploading = false;
  String _status = 'Ready to upload banners';
  final List<String> _logs = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Banner Upload Tool'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Firebase Banner Upload',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'This tool will upload 5 sample banners to your Firebase Firestore database.',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Status: $_status',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _isUploading ? Colors.orange : Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isUploading ? null : _uploadSampleBanners,
              child: _isUploading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Uploading...'),
                      ],
                    )
                  : const Text('Upload Sample Banners'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Upload Logs',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _logs.isEmpty ? 'No logs yet...' : _logs.join('\n'),
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _uploadSampleBanners() async {
    setState(() {
      _isUploading = true;
      _status = 'Starting upload...';
      _logs.clear();
      _logs.add('🚀 Starting upload of sample banners to Firebase...');
    });

    try {
      final firestore = FirebaseFirestore.instance;
      final sampleBanners = _getSampleBanners();
      
      setState(() {
        _logs.add('📋 Found ${sampleBanners.length} sample banners to upload');
      });

      // Upload banners using batch
      final batch = firestore.batch();

      for (final banner in sampleBanners) {
        final docRef = firestore.collection('banners').doc(banner.id);
        final bannerData = banner.toJson();
        // Remove the id from the data since it's used as the document ID
        bannerData.remove('id');
        batch.set(docRef, bannerData);
        
        setState(() {
          _logs.add('📝 Prepared banner: ${banner.title}');
        });
      }

      // Commit batch
      setState(() {
        _logs.add('💾 Committing batch to Firebase...');
      });
      
      await batch.commit();
      
      setState(() {
        _isUploading = false;
        _status = 'Upload completed successfully!';
        _logs.add('✅ Successfully uploaded ${sampleBanners.length} banners to Firebase!');
        _logs.add('🎉 All banners are now available in your Firestore database!');
        _logs.add('📊 Summary:');
        for (final banner in sampleBanners) {
          _logs.add('   - ${banner.title} (Priority: ${banner.priority})');
        }
      });
    } catch (e) {
      setState(() {
        _isUploading = false;
        _status = 'Upload failed!';
        _logs.add('❌ Error uploading sample banners: $e');
      });
    }
  }

  List<BannerModel> _getSampleBanners() {
    final now = DateTime.now();
    final futureDate = now.add(const Duration(days: 30));

    return [
      BannerModel(
        id: 'banner_summer_sale',
        title: 'Summer Sale 2024',
        subtitle: 'Up to 50% off on all automotive parts',
        imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=400&fit=crop',
        actionUrl: '/products?category=sale',
        actionText: 'Shop Now',
        colorHex: '#FF6B35',
        priority: 5,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 2)),
        expiresAt: futureDate,
      ),
      BannerModel(
        id: 'banner_new_arrivals',
        title: 'New Arrivals',
        subtitle: 'Latest automotive accessories and parts',
        imageUrl: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=800&h=400&fit=crop',
        actionUrl: '/products?filter=new',
        actionText: 'Explore',
        colorHex: '#2E86AB',
        priority: 4,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 1)),
        expiresAt: futureDate,
      ),
      BannerModel(
        id: 'banner_premium_oils',
        title: 'Premium Engine Oils',
        subtitle: 'Keep your engine running smooth with quality oils',
        imageUrl: 'https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=800&h=400&fit=crop',
        actionUrl: '/products?category=oils',
        actionText: 'View Oils',
        colorHex: '#A23B72',
        priority: 3,
        isActive: true,
        createdAt: now.subtract(const Duration(hours: 12)),
        expiresAt: futureDate,
      ),
      BannerModel(
        id: 'banner_tire_deals',
        title: 'Tire Special Deals',
        subtitle: 'Get the best deals on premium tires',
        imageUrl: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800&h=400&fit=crop',
        actionUrl: '/products?category=tires',
        actionText: 'Shop Tires',
        colorHex: '#F18F01',
        priority: 2,
        isActive: true,
        createdAt: now.subtract(const Duration(hours: 6)),
        expiresAt: futureDate,
      ),
      BannerModel(
        id: 'banner_free_shipping',
        title: 'Free Shipping Weekend',
        subtitle: 'Free delivery on orders above \$50',
        imageUrl: 'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=800&h=400&fit=crop',
        actionUrl: '/products',
        actionText: 'Start Shopping',
        colorHex: '#C73E1D',
        priority: 1,
        isActive: true,
        createdAt: now.subtract(const Duration(hours: 2)),
        expiresAt: now.add(const Duration(days: 3)), // Shorter expiry for urgency
      ),
    ];
  }
}
