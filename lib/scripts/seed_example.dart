import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';

import '../features/products/data/utils/product_data_seeder.dart';

/// Example of how to use ProductDataSeeder in your app
/// You can call this from anywhere in your app
void main() {
  runApp(const SeedExampleApp());
}

class SeedExampleApp extends StatelessWidget {
  const SeedExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Seed Example',
      home: const SeedExampleScreen(),
    );
  }
}

class SeedExampleScreen extends StatefulWidget {
  const SeedExampleScreen({super.key});

  @override
  State<SeedExampleScreen> createState() => _SeedExampleScreenState();
}

class _SeedExampleScreenState extends State<SeedExampleScreen> {
  bool _isSeeding = false;
  String _status = 'Ready to seed database';

  @override
  void initState() {
    super.initState();
    _initializeFirebase();
  }

  Future<void> _initializeFirebase() async {
    try {
      await Firebase.initializeApp();
      setState(() {
        _status = 'Firebase initialized. Ready to seed.';
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to initialize Firebase: $e';
      });
    }
  }

  Future<void> _seedDatabase() async {
    if (_isSeeding) return;

    setState(() {
      _isSeeding = true;
      _status = 'Seeding database...';
    });

    try {
      final success = await ProductDataSeeder.seedDatabase();
      
      setState(() {
        _isSeeding = false;
        _status = success 
            ? '✅ Database seeded successfully!' 
            : '❌ Failed to seed database';
      });
    } catch (e) {
      setState(() {
        _isSeeding = false;
        _status = '❌ Error: $e';
      });
    }
  }

  Future<void> _forceSeed() async {
    if (_isSeeding) return;

    setState(() {
      _isSeeding = true;
      _status = 'Force seeding database...';
    });

    try {
      final success = await ProductDataSeeder.forceSeed();
      
      setState(() {
        _isSeeding = false;
        _status = success 
            ? '✅ Database force-seeded successfully!' 
            : '❌ Failed to force-seed database';
      });
    } catch (e) {
      setState(() {
        _isSeeding = false;
        _status = '❌ Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Seeder'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                    if (_isSeeding) ...[
                      const SizedBox(height: 16),
                      const LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Instructions
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'How to Use in Your App',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Import the seeder and call it from anywhere in your app:\n\n'
                      'import \'../features/products/data/utils/product_data_seeder.dart\';\n\n'
                      '// Seed only if database is empty\n'
                      'await ProductDataSeeder.seedDatabase();\n\n'
                      '// Force re-seed (clears existing data)\n'
                      'await ProductDataSeeder.forceSeed();',
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isSeeding ? null : _seedDatabase,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Text(_isSeeding ? 'Seeding...' : 'Seed Database'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isSeeding ? null : _forceSeed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Text(_isSeeding ? 'Seeding...' : 'Force Re-seed'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // What gets uploaded
            Card(
              color: Colors.green.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.upload, color: Colors.green.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'What Gets Uploaded',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text('• 12 automotive products (Castrol oil, Bosch brakes, etc.)'),
                    const Text('• 11 product categories (Fluids, Brakes, Filters, etc.)'),
                    const Text('• 12 brands (Castrol, Bosch, K&N, NGK, etc.)'),
                    const Text('• Full product specifications and metadata'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
