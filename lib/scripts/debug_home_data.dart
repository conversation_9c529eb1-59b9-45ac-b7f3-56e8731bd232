import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../firebase_options.dart';

/// Debug script to test home data fetching from Firebase
/// Run with: dart run lib/scripts/debug_home_data.dart
void main() async {
  print('🔍 Home Data Debug Script Starting...');
  
  try {
    // Initialize Firebase
    print('🔧 Initializing Firebase...');
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    print('✅ Firebase initialized successfully');

    final firestore = FirebaseFirestore.instance;
    
    print('\n' + '='*50);
    print('🎯 TESTING BANNER QUERIES');
    print('='*50);
    
    await _testBannerQueries(firestore);
    
    print('\n' + '='*50);
    print('🛍️ TESTING PRODUCT QUERIES');
    print('='*50);
    
    await _testProductQueries(firestore);
    
  } catch (e) {
    print('❌ Error in debug script: $e');
    exit(1);
  }
}

Future<void> _testBannerQueries(FirebaseFirestore firestore) async {
  try {
    // Test 1: Get all banners (simple query)
    print('\n📋 Test 1: Fetching all banners...');
    final allBannersSnapshot = await firestore
        .collection('banners')
        .get();
    
    print('📊 Found ${allBannersSnapshot.docs.length} total banners');
    
    for (final doc in allBannersSnapshot.docs) {
      final data = doc.data();
      print('   📄 Banner ${doc.id}:');
      print('      - title: ${data['title']}');
      print('      - isActive: ${data['isActive']}');
      print('      - expiresAt: ${data['expiresAt']}');
      print('      - priority: ${data['priority']}');
    }
    
    // Test 2: Get active banners (simplified query)
    print('\n📋 Test 2: Fetching active banners (simplified)...');
    final activeBannersSnapshot = await firestore
        .collection('banners')
        .where('isActive', isEqualTo: true)
        .get();
    
    print('📊 Found ${activeBannersSnapshot.docs.length} active banners');
    
    final now = DateTime.now();
    int validBanners = 0;
    
    for (final doc in activeBannersSnapshot.docs) {
      final data = doc.data();
      final expiresAt = data['expiresAt'];
      bool isValid = true;
      
      if (expiresAt != null) {
        DateTime expiry;
        if (expiresAt is Timestamp) {
          expiry = expiresAt.toDate();
        } else if (expiresAt is String) {
          expiry = DateTime.parse(expiresAt);
        } else {
          print('   ⚠️  Unknown expiresAt type: ${expiresAt.runtimeType}');
          continue;
        }
        
        isValid = expiry.isAfter(now);
      }
      
      if (isValid) {
        validBanners++;
        print('   ✅ Valid banner: ${data['title']}');
      } else {
        print('   ❌ Expired banner: ${data['title']}');
      }
    }
    
    print('📊 Valid (non-expired) banners: $validBanners');
    
    // Test 3: Check banner data structure
    print('\n📋 Test 3: Checking banner data structure...');
    try {
      for (final doc in activeBannersSnapshot.docs) {
        final data = doc.data();
        print('   📄 Banner ${doc.id} structure:');
        print('      - Keys: ${data.keys.toList()}');
        print('      - createdAt type: ${data['createdAt']?.runtimeType}');
        print('      - expiresAt type: ${data['expiresAt']?.runtimeType}');

        // Check if dates are valid
        if (data['createdAt'] is Timestamp) {
          final createdAt = (data['createdAt'] as Timestamp).toDate();
          print('      - createdAt value: $createdAt');
        }
        if (data['expiresAt'] is Timestamp) {
          final expiresAt = (data['expiresAt'] as Timestamp).toDate();
          print('      - expiresAt value: $expiresAt');
        }
        break; // Just check first banner
      }
    } catch (e) {
      print('❌ Error checking banner structure: $e');
    }
    
  } catch (e) {
    print('❌ Error testing banner queries: $e');
  }
}

Future<void> _testProductQueries(FirebaseFirestore firestore) async {
  try {
    // Test 1: Get all products
    print('\n🛍️ Test 1: Fetching all products...');
    final allProductsSnapshot = await firestore
        .collection('products')
        .limit(5) // Limit for testing
        .get();
    
    print('📊 Found ${allProductsSnapshot.docs.length} total products (limited to 5)');
    
    for (final doc in allProductsSnapshot.docs) {
      final data = doc.data();
      print('   📦 Product ${doc.id}:');
      print('      - name: ${data['name']}');
      print('      - isFeatured: ${data['isFeatured']}');
      print('      - isActive: ${data['isActive']}');
      print('      - stockQuantity: ${data['stockQuantity']}');
    }
    
    // Test 2: Get featured products
    print('\n🛍️ Test 2: Fetching featured products...');
    final featuredProductsSnapshot = await firestore
        .collection('products')
        .where('isFeatured', isEqualTo: true)
        .where('isActive', isEqualTo: true)
        .limit(10)
        .get();
    
    print('📊 Found ${featuredProductsSnapshot.docs.length} featured products');
    
    int validProducts = 0;
    
    for (final doc in featuredProductsSnapshot.docs) {
      final data = doc.data();
      final stockQuantity = data['stockQuantity'] ?? 0;
      
      if (stockQuantity > 0) {
        validProducts++;
        print('   ✅ Valid product: ${data['name']} (Stock: $stockQuantity)');
      } else {
        print('   ❌ Out of stock: ${data['name']} (Stock: $stockQuantity)');
      }
    }
    
    print('📊 Valid (in-stock) featured products: $validProducts');
    
    // Test 3: Check product data structure
    print('\n🛍️ Test 3: Checking product data structure...');
    try {
      for (final doc in featuredProductsSnapshot.docs) {
        final data = doc.data();
        print('   📦 Product ${doc.id} structure:');
        print('      - Keys: ${data.keys.toList()}');
        print('      - createdAt type: ${data['createdAt']?.runtimeType}');
        print('      - updatedAt type: ${data['updatedAt']?.runtimeType}');
        print('      - isFeatured: ${data['isFeatured']}');
        print('      - isActive: ${data['isActive']}');
        print('      - stockQuantity: ${data['stockQuantity']}');

        // Check if dates are valid
        if (data['createdAt'] is Timestamp) {
          final createdAt = (data['createdAt'] as Timestamp).toDate();
          print('      - createdAt value: $createdAt');
        }
        if (data['updatedAt'] is Timestamp) {
          final updatedAt = (data['updatedAt'] as Timestamp).toDate();
          print('      - updatedAt value: $updatedAt');
        }
        break; // Just check first product
      }
    } catch (e) {
      print('❌ Error checking product structure: $e');
    }
    
  } catch (e) {
    print('❌ Error testing product queries: $e');
  }
}
