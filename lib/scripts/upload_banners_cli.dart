import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../features/home/<USER>/models/banner_model.dart';
import '../firebase_options.dart';

/// Command-line script to upload sample banners to Firebase
/// Run with: dart run lib/scripts/upload_banners_cli.dart
void main() async {
  print('🚀 Banner Upload Script Starting...');
  
  try {
    // Initialize Firebase
    print('🔧 Initializing Firebase...');
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    print('✅ Firebase initialized successfully');

    // Get Firestore instance
    final firestore = FirebaseFirestore.instance;
    
    // Generate sample banners
    final sampleBanners = _getSampleBanners();
    print('📋 Generated ${sampleBanners.length} sample banners');

    // Ask for confirmation
    print('\n📝 Banners to upload:');
    for (int i = 0; i < sampleBanners.length; i++) {
      final banner = sampleBanners[i];
      print('   ${i + 1}. ${banner.title} - ${banner.subtitle}');
    }
    
    print('\n⚠️  This will upload banners to your Firebase project: mobby-zapp');
    stdout.write('Do you want to continue? (y/N): ');
    final input = stdin.readLineSync()?.toLowerCase();
    
    if (input != 'y' && input != 'yes') {
      print('❌ Upload cancelled by user');
      exit(0);
    }

    // Upload banners using batch
    print('\n💾 Starting batch upload...');
    final batch = firestore.batch();

    for (final banner in sampleBanners) {
      final docRef = firestore.collection('banners').doc(banner.id);
      final bannerData = banner.toJson();
      // Remove the id from the data since it's used as the document ID
      bannerData.remove('id');
      batch.set(docRef, bannerData);
      print('📝 Prepared: ${banner.title}');
    }

    // Commit batch
    print('🔄 Committing to Firebase...');
    await batch.commit();
    
    print('\n✅ Successfully uploaded ${sampleBanners.length} banners to Firebase!');
    print('🎉 All banners are now available in your Firestore database!');
    
    print('\n📊 Upload Summary:');
    for (final banner in sampleBanners) {
      print('   ✓ ${banner.title} (Priority: ${banner.priority}, Active: ${banner.isActive})');
    }
    
    print('\n🔍 You can verify the upload in Firebase Console:');
    print('   https://console.firebase.google.com/project/mobby-zapp/firestore/data/banners');
    
  } catch (e) {
    print('❌ Error uploading banners: $e');
    exit(1);
  }
}

List<BannerModel> _getSampleBanners() {
  final now = DateTime.now();
  final futureDate = now.add(const Duration(days: 30));

  return [
    BannerModel(
      id: 'banner_summer_sale',
      title: 'Summer Sale 2024',
      subtitle: 'Up to 50% off on all automotive parts',
      imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=400&fit=crop',
      actionUrl: '/products?category=sale',
      actionText: 'Shop Now',
      colorHex: '#FF6B35',
      priority: 5,
      isActive: true,
      createdAt: now.subtract(const Duration(days: 2)),
      expiresAt: futureDate,
    ),
    BannerModel(
      id: 'banner_new_arrivals',
      title: 'New Arrivals',
      subtitle: 'Latest automotive accessories and parts',
      imageUrl: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=800&h=400&fit=crop',
      actionUrl: '/products?filter=new',
      actionText: 'Explore',
      colorHex: '#2E86AB',
      priority: 4,
      isActive: true,
      createdAt: now.subtract(const Duration(days: 1)),
      expiresAt: futureDate,
    ),
    BannerModel(
      id: 'banner_premium_oils',
      title: 'Premium Engine Oils',
      subtitle: 'Keep your engine running smooth with quality oils',
      imageUrl: 'https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=800&h=400&fit=crop',
      actionUrl: '/products?category=oils',
      actionText: 'View Oils',
      colorHex: '#A23B72',
      priority: 3,
      isActive: true,
      createdAt: now.subtract(const Duration(hours: 12)),
      expiresAt: futureDate,
    ),
    BannerModel(
      id: 'banner_tire_deals',
      title: 'Tire Special Deals',
      subtitle: 'Get the best deals on premium tires',
      imageUrl: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800&h=400&fit=crop',
      actionUrl: '/products?category=tires',
      actionText: 'Shop Tires',
      colorHex: '#F18F01',
      priority: 2,
      isActive: true,
      createdAt: now.subtract(const Duration(hours: 6)),
      expiresAt: futureDate,
    ),
    BannerModel(
      id: 'banner_free_shipping',
      title: 'Free Shipping Weekend',
      subtitle: 'Free delivery on orders above \$50',
      imageUrl: 'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=800&h=400&fit=crop',
      actionUrl: '/products',
      actionText: 'Start Shopping',
      colorHex: '#C73E1D',
      priority: 1,
      isActive: true,
      createdAt: now.subtract(const Duration(hours: 2)),
      expiresAt: now.add(const Duration(days: 3)), // Shorter expiry for urgency
    ),
  ];
}
