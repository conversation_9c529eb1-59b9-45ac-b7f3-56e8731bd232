import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../features/products/data/sample_products.dart';
import '../features/products/data/models/product_model.dart';

/// Simple Flutter app to upload sample products to Firebase
/// Run this once to populate your database with demo products
void main() {
  runApp(const ProductUploadApp());
}

class ProductUploadApp extends StatelessWidget {
  const ProductUploadApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Product Upload',
      home: const ProductUploadScreen(),
    );
  }
}

class ProductUploadScreen extends StatefulWidget {
  const ProductUploadScreen({super.key});

  @override
  State<ProductUploadScreen> createState() => _ProductUploadScreenState();
}

class _ProductUploadScreenState extends State<ProductUploadScreen> {
  bool _isUploading = false;
  String _status = 'Ready to upload sample products';
  final List<String> _logs = [];

  @override
  void initState() {
    super.initState();
    _initializeFirebase();
  }

  Future<void> _initializeFirebase() async {
    try {
      await Firebase.initializeApp();
      setState(() {
        _status = 'Firebase initialized. Ready to upload.';
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to initialize Firebase: $e';
      });
    }
  }

  Future<void> _uploadProducts() async {
    if (_isUploading) return;

    setState(() {
      _isUploading = true;
      _status = 'Uploading sample products...';
      _logs.clear();
    });

    await _uploadSampleProducts();
  }

  Future<void> _uploadSampleProducts() async {
    try {
      final firestore = FirebaseFirestore.instance;

      setState(() {
        _status = 'Starting upload...';
        _logs.add('🚀 Starting upload of sample products to Firebase...');
      });

      // Get sample products
      final sampleProducts = SampleProducts.getAllProducts();
      setState(() {
        _logs.add(
          '📦 Found ${sampleProducts.length} sample products to upload',
        );
      });

      // Upload products using batch
      final batch = firestore.batch();

      for (final product in sampleProducts) {
        final productModel = ProductModel.fromEntity(product);
        final docRef = firestore.collection('products').doc(product.id);
        batch.set(docRef, productModel.toJson());
      }

      // Commit batch
      await batch.commit();
      setState(() {
        _logs.add(
          '✅ Successfully uploaded ${sampleProducts.length} products to Firebase!',
        );
      });

      // Upload categories
      await _uploadCategories(firestore);

      // Upload brands
      await _uploadBrands(firestore);

      setState(() {
        _isUploading = false;
        _status = 'Upload completed successfully!';
        _logs.add('🎉 All sample data uploaded successfully!');
        _logs.add('📊 Summary:');
        _logs.add('   - ${sampleProducts.length} products');
        _logs.add('   - 11 categories');
        _logs.add('   - 12 brands');
      });
    } catch (e) {
      setState(() {
        _isUploading = false;
        _status = 'Upload failed!';
        _logs.add('❌ Error uploading sample products: $e');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Sample Products'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                    if (_isUploading) ...[
                      const SizedBox(height: 16),
                      const LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Upload Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isUploading ? null : _uploadProducts,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  _isUploading ? 'Uploading...' : 'Upload Sample Products',
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Logs
            if (_logs.isNotEmpty) ...[
              Text(
                'Upload Logs',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: ListView.builder(
                      itemCount: _logs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2.0),
                          child: Text(
                            _logs[index],
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Upload product categories
Future<void> _uploadCategories(FirebaseFirestore firestore) async {
  print('📂 Uploading categories...');

  final categories = [
    {
      'id': 'cat_fluids',
      'name': 'Fluids & Lubricants',
      'description':
          'Engine oils, transmission fluids, and other automotive lubricants',
      'isActive': true,
      'sortOrder': 1,
    },
    {
      'id': 'cat_brakes',
      'name': 'Brakes & Suspension',
      'description': 'Brake pads, rotors, suspension components',
      'isActive': true,
      'sortOrder': 2,
    },
    {
      'id': 'cat_filters',
      'name': 'Filters',
      'description': 'Air filters, oil filters, fuel filters',
      'isActive': true,
      'sortOrder': 3,
    },
    {
      'id': 'cat_ignition',
      'name': 'Ignition System',
      'description': 'Spark plugs, ignition coils, wires',
      'isActive': true,
      'sortOrder': 4,
    },
    {
      'id': 'cat_electrical',
      'name': 'Electrical',
      'description': 'Batteries, alternators, starters',
      'isActive': true,
      'sortOrder': 5,
    },
    {
      'id': 'cat_tires',
      'name': 'Tires & Wheels',
      'description': 'Tires, wheels, tire accessories',
      'isActive': true,
      'sortOrder': 6,
    },
    {
      'id': 'cat_lighting',
      'name': 'Lighting',
      'description': 'Headlights, taillights, LED bulbs',
      'isActive': true,
      'sortOrder': 7,
    },
    {
      'id': 'cat_interior',
      'name': 'Interior Accessories',
      'description': 'Floor mats, seat covers, organizers',
      'isActive': true,
      'sortOrder': 8,
    },
    {
      'id': 'cat_care',
      'name': 'Car Care',
      'description': 'Wax, polish, cleaning supplies',
      'isActive': true,
      'sortOrder': 9,
    },
    {
      'id': 'cat_maintenance',
      'name': 'Maintenance',
      'description': 'Wiper blades, belts, hoses',
      'isActive': true,
      'sortOrder': 10,
    },
    {
      'id': 'cat_emergency',
      'name': 'Emergency & Safety',
      'description': 'Jumper cables, emergency kits, safety equipment',
      'isActive': true,
      'sortOrder': 11,
    },
  ];

  final batch = firestore.batch();

  for (final category in categories) {
    final docRef = firestore
        .collection('categories')
        .doc(category['id'] as String);
    batch.set(docRef, category);
  }

  await batch.commit();
  print('✅ Uploaded ${categories.length} categories');
}

/// Upload product brands
Future<void> _uploadBrands(FirebaseFirestore firestore) async {
  print('🏢 Uploading brands...');

  final brands = [
    {
      'id': 'brand_castrol',
      'name': 'Castrol',
      'description': 'Premium motor oils and lubricants',
      'isActive': true,
      'sortOrder': 1,
    },
    {
      'id': 'brand_bosch',
      'name': 'Bosch',
      'description': 'Automotive parts and accessories',
      'isActive': true,
      'sortOrder': 2,
    },
    {
      'id': 'brand_kn',
      'name': 'K&N',
      'description': 'High-performance air filters',
      'isActive': true,
      'sortOrder': 3,
    },
    {
      'id': 'brand_ngk',
      'name': 'NGK',
      'description': 'Spark plugs and ignition components',
      'isActive': true,
      'sortOrder': 4,
    },
    {
      'id': 'brand_optima',
      'name': 'Optima',
      'description': 'High-performance batteries',
      'isActive': true,
      'sortOrder': 5,
    },
    {
      'id': 'brand_michelin',
      'name': 'Michelin',
      'description': 'Premium tires and automotive services',
      'isActive': true,
      'sortOrder': 6,
    },
    {
      'id': 'brand_sealight',
      'name': 'SEALIGHT',
      'description': 'LED automotive lighting',
      'isActive': true,
      'sortOrder': 7,
    },
    {
      'id': 'brand_weathertech',
      'name': 'WeatherTech',
      'description': 'Automotive floor protection and accessories',
      'isActive': true,
      'sortOrder': 8,
    },
    {
      'id': 'brand_meguiars',
      'name': 'Meguiar\'s',
      'description': 'Car care and detailing products',
      'isActive': true,
      'sortOrder': 9,
    },
    {
      'id': 'brand_rainx',
      'name': 'Rain-X',
      'description': 'Windshield treatments and wiper blades',
      'isActive': true,
      'sortOrder': 10,
    },
    {
      'id': 'brand_valvoline',
      'name': 'Valvoline',
      'description': 'Motor oils and automotive fluids',
      'isActive': true,
      'sortOrder': 11,
    },
    {
      'id': 'brand_cartman',
      'name': 'CARTMAN',
      'description': 'Automotive tools and emergency equipment',
      'isActive': true,
      'sortOrder': 12,
    },
  ];

  final batch = firestore.batch();

  for (final brand in brands) {
    final docRef = firestore.collection('brands').doc(brand['id'] as String);
    batch.set(docRef, brand);
  }

  await batch.commit();
  print('✅ Uploaded ${brands.length} brands');
}
